require 'rails_helper'

RSpec.describe CompanyModule::CustomForms::EntityCreate do
  let(:company) { create(:company) }
  let(:current_user) { create(:company_user, company: company) }
  let(:custom_form) { create(:custom_form, company: company, company_module: 'company_user') }
  
  describe '#create_bulk_user_activity' do
    let(:service) { described_class.new(current_user: current_user) }
    
    context 'when company_users is empty' do
      it 'returns early without creating activities' do
        expect(CompanyUserActivity).not_to receive(:insert_all)
        service.create_bulk_user_activity([])
      end
    end
    
    context 'when company_users are present' do
      let!(:discovered_user1) { create(:discovered_user, email: '<EMAIL>', company: company) }
      let!(:discovered_user2) { create(:discovered_user, email: '<EMAIL>', company: company) }
      let!(:discovered_user_source1) { create(:discovered_user_source, discovered_user: discovered_user1, source: :genuity_active_directory) }
      let!(:discovered_user_source2) { create(:discovered_user_source, discovered_user: discovered_user2, source: :microsoft_active_directory) }
      
      let(:user1) { create(:user, email: '<EMAIL>') }
      let(:user2) { create(:user, email: '<EMAIL>') }
      let(:company_user1) { create(:company_user, user: user1, company: company, custom_form: custom_form) }
      let(:company_user2) { create(:company_user, user: user2, company: company, custom_form: custom_form) }
      
      it 'creates bulk user activities with correct sources' do
        company_users = [company_user1, company_user2]
        
        expect(CompanyUserActivity).to receive(:insert_all) do |activities|
          expect(activities.length).to eq(2)
          
          activity1 = activities.find { |a| a[:company_user_id] == company_user1.id }
          activity2 = activities.find { |a| a[:company_user_id] == company_user2.id }
          
          expect(activity1[:source]).to eq('genuity_active_directory')
          expect(activity1[:activity_type]).to eq(CompanyUserActivity.activity_types["imported"])
          expect(activity1[:owner_id]).to eq(current_user.id)
          
          expect(activity2[:source]).to eq('microsoft_active_directory')
          expect(activity2[:activity_type]).to eq(CompanyUserActivity.activity_types["imported"])
          expect(activity2[:owner_id]).to eq(current_user.id)
        end
        
        service.create_bulk_user_activity(company_users)
      end
      
      it 'handles users without discovered_user_sources gracefully' do
        # Create a company user without corresponding discovered user
        user3 = create(:user, email: '<EMAIL>')
        company_user3 = create(:company_user, user: user3, company: company, custom_form: custom_form)
        
        company_users = [company_user1, company_user3]
        
        expect(CompanyUserActivity).to receive(:insert_all) do |activities|
          expect(activities.length).to eq(2)
          
          activity1 = activities.find { |a| a[:company_user_id] == company_user1.id }
          activity3 = activities.find { |a| a[:company_user_id] == company_user3.id }
          
          expect(activity1[:source]).to eq('genuity_active_directory')
          expect(activity3[:source]).to be_nil # No discovered user found
        end
        
        service.create_bulk_user_activity(company_users)
      end
      
      it 'optimizes database queries by batch loading' do
        company_users = [company_user1, company_user2]
        
        # Expect only one query to load discovered users with sources
        expect(DiscoveredUser).to receive(:includes).with(:discovered_user_sources).and_call_original
        
        # The method should not call find_by for each user individually
        expect(DiscoveredUser).not_to receive(:find_by)
        
        service.create_bulk_user_activity(company_users)
      end
    end
  end
  
  describe '#build_discovered_users_lookup' do
    let(:service) { described_class.new(current_user: current_user) }
    
    context 'when email_company_pairs is empty' do
      it 'returns empty hash' do
        result = service.send(:build_discovered_users_lookup, [])
        expect(result).to eq({})
      end
    end
    
    context 'when email_company_pairs are present' do
      let!(:discovered_user1) { create(:discovered_user, email: '<EMAIL>', company: company) }
      let!(:discovered_user2) { create(:discovered_user, email: '<EMAIL>', company: company) }
      let!(:discovered_user_source1) { create(:discovered_user_source, discovered_user: discovered_user1, source: :genuity_active_directory) }
      let!(:discovered_user_source2) { create(:discovered_user_source, discovered_user: discovered_user2, source: :microsoft_active_directory) }
      
      it 'builds correct lookup hash' do
        email_company_pairs = [
          ['<EMAIL>', company.id],
          ['<EMAIL>', company.id]
        ]
        
        result = service.send(:build_discovered_users_lookup, email_company_pairs)
        
        expect(result["user1@test.com_#{company.id}"]).to eq('genuity_active_directory')
        expect(result["user2@test.com_#{company.id}"]).to eq('microsoft_active_directory')
      end
      
      it 'handles users without sources' do
        discovered_user_without_source = create(:discovered_user, email: '<EMAIL>', company: company)
        
        email_company_pairs = [
          ['<EMAIL>', company.id],
          ['<EMAIL>', company.id]
        ]
        
        result = service.send(:build_discovered_users_lookup, email_company_pairs)
        
        expect(result["user1@test.com_#{company.id}"]).to eq('genuity_active_directory')
        expect(result["user3@test.com_#{company.id}"]).to be_nil
      end
    end
  end
end
