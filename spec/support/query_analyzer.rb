class QueryA<PERSON>yzer
  def self.analyze(&block)
    queries = []
    query_counts = Hash.new(0)
    
    callback = lambda do |name, started, finished, unique_id, payload|
      if payload[:sql] && !payload[:name]&.include?('SCHEMA')
        sql = payload[:sql]
        queries << sql
        
        # Categorize queries
        case sql
        when /SELECT.*FROM "discovered_users"/i
          query_counts[:discovered_users] += 1
        when /SELECT.*FROM "custom_form_fields"/i
          query_counts[:custom_form_fields] += 1
        when /SELECT.*FROM "custom_form_values"/i
          query_counts[:custom_form_values] += 1
        when /SELECT.*FROM "company_users"/i
          query_counts[:company_users] += 1
        when /SELECT.*FROM "contributors"/i
          query_counts[:contributors] += 1
        when /SELECT.*FROM "linkables"/i
          query_counts[:linkables] += 1
        when /INSERT INTO "contributors"/i
          query_counts[:contributor_inserts] += 1
        when /INSERT INTO "linkables"/i
          query_counts[:linkable_inserts] += 1
        when /INSERT INTO "company_user_activities"/i
          query_counts[:activity_inserts] += 1
        when /UPDATE.*company_users/i
          query_counts[:company_user_updates] += 1
        else
          query_counts[:other] += 1
        end
      end
    end
    
    ActiveSupport::Notifications.subscribed(callback, 'sql.active_record') do
      yield
    end
    
    total_queries = queries.length
    
    puts "\n" + "="*60
    puts "📊 QUERY ANALYSIS REPORT"
    puts "="*60
    puts "🔍 Total Queries: #{total_queries}"
    puts "\n📋 Query Breakdown:"
    
    query_counts.sort_by { |k, v| -v }.each do |type, count|
      percentage = (count.to_f / total_queries * 100).round(1)
      puts "  #{type.to_s.ljust(25)}: #{count.to_s.rjust(3)} queries (#{percentage}%)"
    end
    
    puts "\n🎯 N+1 Analysis:"
    if query_counts[:discovered_users] > 5
      puts "  ⚠️  DiscoveredUser N+1 detected: #{query_counts[:discovered_users]} queries"
    else
      puts "  ✅ DiscoveredUser optimized: #{query_counts[:discovered_users]} queries"
    end
    
    if query_counts[:custom_form_fields] > 5
      puts "  ⚠️  CustomFormField N+1 detected: #{query_counts[:custom_form_fields]} queries"
    else
      puts "  ✅ CustomFormField optimized: #{query_counts[:custom_form_fields]} queries"
    end
    
    if query_counts[:custom_form_values] > 10
      puts "  ⚠️  CustomFormValue N+1 detected: #{query_counts[:custom_form_values]} queries"
    else
      puts "  ✅ CustomFormValue optimized: #{query_counts[:custom_form_values]} queries"
    end
    
    puts "="*60
    
    { total: total_queries, breakdown: query_counts, queries: queries }
  end
end
