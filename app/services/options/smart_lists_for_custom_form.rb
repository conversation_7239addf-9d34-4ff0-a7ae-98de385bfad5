class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    if @params[:name] == 'Contributor'
      list_options = Contributor.includes(company_user: :user, group: {}, guest: {}).where(id: options_ids)
      my_options << list_options.map { |option| { id: option.id, name: option.name } }
      related_item_options = linkables.select{ |item| ( item.linkable_type == 'CompanyUser' || item.linkable_type == 'Group' )}
      my_options << load_contributor_ids(related_item_options)
    else
      list_options = @params[:name].constantize.where(id: options_ids)
      my_options << list_options.map { |option| { id: option.id, name: option.name } }
      related_item_options = linkables.select{ |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map{ |opt| { id: opt.linkable_id, name: opt.name } }
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
  def offset
    @params[:offset].to_i if @params[:offset]
  end

  def limit
    @params[:limit].to_i if @params[:limit]
  end

  def fetch_options      
    options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                        .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                        .pluck(:value_int)
                                                        .uniq
    @total_count = options_query.length
    options_query
  end

  def fetch_linkables
    linkables_query = LinkableLink.joins(:source)
                                  .includes(:target)
                                  .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                  .map{ |item| item.target }
    if (@params[:name] != "ManagedAsset" && @total_count == 0)
      @total_count = linkables_query.select { |item| item.linkable_type == @params[:name]}.length
    end
    linkables_query
  end

  def load_contributor_ids(related_item_options)
    grouped_items = related_item_options.group_by(&:linkable_type)
    contributor_map = {}
    grouped_items.each do |type, items|
      ids = items.map(&:linkable_id)
      type.constantize.where(id: ids).pluck(:id, :contributor_id).each do |id, contributor_id|
        contributor_map[id] = contributor_id
      end
    end

    related_item_options.map { |opt| { id: contributor_map[opt.linkable_id], name: opt.name } }
  end
end
