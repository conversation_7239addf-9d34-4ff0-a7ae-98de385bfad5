class CompanyUser < ActiveRecord::Base
  include Validations
  include Rewards
  include Events
  #PgSearch is replced with PgSearch::Model in rails 6.0.0
  include PgSearch::Model
  include Linkables
  include CompanyUserValues
  include UniquelyIdentified
  include WithContributors
  include DeleteAutomatedTaskCacheKey
  include HandleCompanyCacheKeys
  include QuickViewFilterUpdate
  include CompanyUserCache
  include UserCache
  include CompanyCache
  extend HandleCompanyCacheKeys

  attr_accessor :skip_callbacks, :skip_validations, :activity_owner_id, :activity_type, :current_user, :skip_assignment_callback,
                :company_guests, :helpdesk_cf_ids, :integration_users, :preloaded_existing_users, :preloaded_custom_form_values

  pg_search_scope :search_text,
                  associated_against: {
                    user: [:first_name, :last_name]
                  },
                  using: {
                    tsearch: {
                      prefix: true,
                      any_word: true
                    }
                  }

  enum self_onboarding: ["uninvited", "invited", "onboard", "invite_pending"]

  has_one :experience_point
  has_one :dashboard_preference, dependent: :destroy
  has_one :asset_insight_widget, dependent: :destroy
  has_many :subscriptions, foreign_key: :subscriber_id, dependent: :nullify
  has_one :default_workspace, foreign_key: :default_workspace_id
  has_one :integrations_user, class_name: "Integrations::User", dependent: :destroy
  has_many :quick_view_filters, dependent: :destroy

  belongs_to :user
  belongs_to :company
  belongs_to :location
  belongs_to :contributor, dependent: :destroy
  belongs_to :custom_form
  belongs_to :parent_company_user, class_name: "CompanyUser", foreign_key: "parent_company_user_id", optional: true
  belongs_to :granted_by, class_name: "CompanyUser", foreign_key: "granted_by_id", optional: true

  has_many :mfa_sessions, dependent: :destroy
  has_many :feature_request_votes, dependent: :destroy
  has_many :expanded_privileges, through: :contributor
  has_many :feature_requests, foreign_key: :creator_id, dependent: :nullify
  has_many :feature_request_comments, foreign_key: :creator_id, dependent: :nullify
  has_many :feature_request_attachments, foreign_key: :creator_id, dependent: :nullify

  has_many :primary_vendors, class_name: 'Vendor', foreign_key: 'primary_internal_contact_id', dependent: :nullify
  has_many :secondary_vendors, class_name: 'Vendor', foreign_key: 'secondary_internal_contact_id', dependent: :nullify
  has_many :plaid_items
  has_many :plaid_accounts
  has_many :general_transactions
  has_many :managed_assignments, class_name: :AssignmentInformation, foreign_key: :managed_by_contributor_id
  has_many :used_assignments, class_name: :AssignmentInformation, foreign_key: :used_by_contributor_id
  has_many :gamification_achievements_assignments
  has_many :gamification_achievements, :through => :gamification_achievements_assignments
  has_many :contract_contacts, dependent: :destroy
  has_many :contracts, through: :contract_contacts
  has_many :help_ticket_activities, foreign_key: :owner_id
  has_many :custom_form_activities, foreign_key: :owner_id
  has_many :subscription_activities, foreign_key: :owner_id
  has_many :company_user_mailers, dependent: :destroy
  has_many :group_members, through: :contributor
  has_many :groups, through: :group_members
  has_many :user_accesses, dependent: :destroy
  has_many :custom_reports, foreign_key: :creator_id, dependent: :nullify
  has_many :it_reports, foreign_key: :creator_id, dependent: :nullify
  has_many :time_spents, dependent: :nullify
  has_many :created_telecom_services, class_name: "TelecomProvider"
  has_many :created_telecom_providers, class_name: "TelecomService"
  has_many :custom_form_values, as: :module, dependent: :destroy, :autosave => true
  has_many :custom_form_fields, through: :custom_form_values
  has_one :field_mapping, as: :entity, dependent: :destroy
  has_many :activities, class_name: "CompanyUserActivity", dependent: :destroy
  has_many :apps, through: :integrations_user
  has_many :library_documents, foreign_key: :creator_id, dependent: :nullify
  has_many :articles, dependent: :nullify
  has_many :msp_templates_group_members, class_name: "Msp::Templates::GroupMember", dependent: :destroy
  has_many :help_ticket_drafts, dependent: :destroy
  has_many :discovered_asset_histories, dependent: :nullify
  has_many :asset_connector_logs, dependent: :nullify
  has_one :kaseya_config, class_name: "Integrations::Kaseya::Config", dependent: :nullify
  has_one :ubiquiti_config, class_name: "Integrations::Ubiquiti::Config", dependent: :nullify
  has_one :meraki_config, class_name: "Integrations::Meraki::Config", dependent: :nullify
  has_one :sophos_config, class_name: "Integrations::Sophos::Config", dependent: :nullify

  has_many :msp_asset_types, class_name: "Msp::Templates::AssetType", dependent: :nullify, foreign_key: :last_updated_by_id 
  has_many :msp_categories, class_name: "Msp::Templates::Category", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :msp_templates_documents, class_name: "Msp::Templates::Document", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :msp_snippets, class_name: "Msp::Templates::Snippet", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :msp_templates_custom_forms, class_name: "Msp::Templates::CustomForm", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :msp_templates_helpdesk_faqs, class_name: "Msp::Templates::HelpdeskFaq", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :msp_automated_tasks, class_name: "Msp::Templates::AutomatedTask", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :msp_blocked_keywords, class_name: "Msp::Templates::BlockedKeyword", dependent: :nullify, foreign_key: :last_updated_by_id
  has_many :applied_build_data_sets, class_name: "AppliedBuildDataSet", dependent: :nullify, foreign_key: :build_by_id

  accepts_nested_attributes_for :company, allow_destroy: true
  accepts_nested_attributes_for :user

  scope :active, -> { where(archived_at: nil).access_granted } 
  scope :uninvited, -> { where(invitation_due_at: nil, granted_access_at: nil) }

  scope :access_granted, -> { where.not(granted_access_at: nil) }
  scope :not_sample, -> { joins(:company).where('companies.is_sample_company = false') }
  scope :not_sample_company_user, -> { where(is_sample_company_user: false) }

  before_create :set_event_type, :set_users_notification_status, unless: Proc.new { |cu| cu.is_sample_company_user }
  validate :is_user_or_company_id_changed?, on: :update, unless: Proc.new { |cu| cu.skip_validations }

  before_commit Proc.new{ set_created_by(current_user) }, on: :create, if: ->(cu) { !cu.super_admin? }
  after_commit :convert_ticket_emails, on: [:create], unless: Proc.new { |cu| cu.is_sample_company_user }
  after_commit :award_points_to_user, unless: Proc.new { |cu| cu.is_sample_company_user }
  after_commit :create_remote_user,     on: :create, unless: Proc.new { |cu| cu.is_sample_company_user }
  after_commit :create_intercom_user,   on: :create, unless: Proc.new { |cu| cu.is_sample_company_user }
  after_commit :create_intercom_event,  on: :create, unless: Proc.new { |cu| cu.is_sample_company_user }
  after_commit :update_help_ticket_creator, on: [:create]
  after_commit :set_form_field_permissions, on: [:create]
  after_commit :link_with_integration_user, on: [:create]
  after_commit :create_company_ids_cache, on: :create, unless: Proc.new { |cu| cu.is_sample_company_user || cu.super_admin?}
  after_update :remove_associated_assets, if: Proc.new { |r| r.saved_change_to_attribute?(:archived_at) && !r.skip_assignment_callback }
  after_update :remove_company_user_linkables, if: -> { saved_changes[:archived_at]&.last.present? }
  after_update :remove_company_user_vendor_contacts, if: -> { saved_changes[:archived_at]&.last.present? }

  after_create :create_contributor
  after_create -> { delete_options_cache_keys('company_options') }
  after_create -> { delete_options_cache_keys(nil, 'general_company_options') }
  after_update :delete_task_recipients_keys, if: :archived_or_status_changed?
  after_update -> { delete_options_cache_keys('show_company_user') }
  after_update -> { delete_options_cache_keys('workspace_options') }, if: -> { saved_changes[:default_workspace_id].present? }
  after_save :delete_options_cache_keys, :delete_company_user_cache_keys, :delete_contributor_company_user_cache_keys, :delete_company_user_find_cache_keys
  after_destroy :delete_company_user_cache_keys, :delete_company_user_find_cache_keys
  after_create :create_company_domain
  after_destroy :destroy_company_domain

  before_create :assign_default_location
  before_destroy :delete_task_recipients_keys, if: Proc.new { |u| !u.skip_callbacks}
  before_destroy :remove_associations, :update_discovered_user_status, :update_subscription, if: Proc.new { |u| !u.skip_callbacks}
  before_destroy :populate_name_in_event_logs, :delete_company_ids_cache
  after_destroy -> { delete_options_cache_keys('show_company_user') }
  after_commit :sync_monitoring_remote_user,
               on: :create,
               unless: Proc.new { |cu| cu.is_sample_company_user || cu.company.is_sample_company? }
  after_commit :destroy_user, on: :destroy, if: Proc.new { |u| !u.skip_callbacks}

  after_commit :create_user_activity, on: [:update]
  after_commit -> { check_quick_view_filter(true) }, on: :destroy

  delegate :privileges, to: :contributor
  delegate :email, :first_name, :last_name, :full_name, :super_admin?, to: :user

  before_validation :check_guests, on: :create
  before_validation :format_numbers
  before_destroy :store_user_name_in_asset_connector_log
  validates_presence_of :user, :company
  validates :work_phone, format: { with: phone_number_regex }, allow_blank: true
  validates :mobile_phone, format: { with: phone_number_regex }, allow_blank: true
  validates_uniqueness_of :user_id, scope: [:company_id], unless: :use_preloaded_validation?
  validate :validate_uniqueness_with_preloaded_data, if: :use_preloaded_validation?

  alias :name :full_name

  def destroy_user
    self.user.destroy! if self.user.company_users.not_sample.count == 0
  end

  def self.find_by_cache(params = nil)
    return find_by(params) unless self.is_cache_enabled?('company_user_find_by')

    params = params.transform_values { |param| param.is_a?(String) ? param : param&.to_s }
    cache_key = "company_user_find_by_#{params}".gsub(/[\\"]/, "'")

    valid_single_key = params.keys.count == 1 && [:id, :guid, :contributor_id].include?(params.keys.first)
    valid_double_key = !valid_single_key && params.keys.count == 2 && params.keys == [:user_id, :company_id]
    
    if valid_single_key || valid_double_key
      Rails.cache.fetch(cache_key, expires_in: 24.hours, skip_nil: true) do
        find_by(params)
      end
    else
      find_by(params)
    end
  end

  def helpdesk_agent?
    company.helpdesk_agents.contributor.contains?(self.contributor_id)
  end

  def is_workspace_agent?(workspace)
    workspace.workspace_agents.contributor.contains?(self.contributor_id)
  end

  def handle_name_change
    check_quick_view_filter
  end

  def sms_number
    return nil if self.mobile_phone.blank?
    country_code = self.mobile_phone.split(",")[0].strip
    country_code_number = ISO3166::Country.new(country_code).country_code
    mobile_phone = self.mobile_phone.split(",")[1].strip.gsub(/[^0-9A-Za-z]/, '')
    if mobile_phone[0] == 1
      "+#{country_code_number || 1}#{mobile_phone[1..-1]}"
    else
      # Area codes in North America cannot start with a 0 or 1 so no need to remove first digit
      "+#{country_code_number || 1}#{mobile_phone}"
    end
  end

  def archived?
    !!archived_at
  end

  def active?
    archived_at.nil?
  end

  def as_json(options = nil)
    json_hash = super(options)
    json_hash = json_hash.merge({user: user.as_json, company: company.as_json(options)})
    json_hash[:is_agent] = self.helpdesk_agent?
    json_hash[:location] = location.name if location

    json_hash[:used_assets_length] = AssignmentInformation.joins(managed_asset: :company)
      .where(managed_assets: { company_id: company.id }, assignment_informations: { used_by_contributor_id: self.contributor_id })&.count

    json_hash[:managed_assets_length] = AssignmentInformation.joins(managed_asset: :company)
      .where(managed_assets: { company_id: company.id }, assignment_informations: { managed_by_contributor_id: self.contributor_id })&.count

    json_hash[:contracts_length] = self.contracts.size
    json_hash[:values] = user_custom_form_values
    json_hash[:custom_form] = {
      id: self.custom_form_id,
      name: self.custom_form&.form_name,
      form_fields: user_custom_form_fields.as_json
    }
    if self.user_avatar.present? && self.user_avatar.as_json[:attachment].present?
      json_hash['avatar_thumb_url'] = self.user_avatar.as_json[:attachment][:attachment_thumb_url]
    else
      json_hash['avatar_url']       = nil
      json_hash['avatar_thumb_url'] = nil
      json_hash['avatar_filename']  = nil
      json_hash['avatar_filesize']  = nil
    end

    json_hash
  end

  def as_json_abbreviated
    {
      id: id,
      name: full_name,
      avatar: avatar.present? ? avatar_thumb : nil,
    }
  end

  def privilege_network_monitoring
    if privileged_write_network_monitoring
      'write'
    elsif privileged_read_network_monitoring
      'read'
    else
      nil
    end
  end

  def privileged_read_network_monitoring
    self.expanded_privileges.exists?(name: "Monitoring", permission_type: "read") || user.super_admin? 
  end

  def privileged_write_network_monitoring
    self.expanded_privileges.exists?(name: "Monitoring", permission_type: "write") || user.super_admin? 
  end

  def privileged_write_company
    self.expanded_privileges.exists?(name: "CompanyUser", permission_type: "write") || user.super_admin?
  end

  def user_custom_form_fields
    if self.custom_form.present?
      self.custom_form.custom_form_fields.includes(:field_position, :custom_form_field_permissions)
        .where.not(name: ["first_name", "last_name", "email"])
        .order('order_position asc')
    end
  end

  def user_custom_form_values
    self.custom_form_values.joins(:custom_form_field)
      .includes(:custom_form_attachment)
      .where.not(custom_form_fields: { name: ["first_name", "last_name", "email"] })
  end

  def populate_name_in_event_logs
    ticket_activities = HelpTicketActivity.where(owner_id: self.id)
    event_logs = EventLog.where(owner_id: self.id)
    ticket_activities.find_each do |activity|
      activity[:data] = activity[:data].merge({ owner_name: self.name })
      activity.save
    end

    event_logs.find_each do |activity|
      activity[:data] = activity[:data].merge({ owner_name: self.name })
      activity.save
    end
  end

  def invite_user
    self.invite_token = SecureRandom.hex(32)
    self.invitation_due_at = DateTime.current + 7.days
    self.save!(validate: false)
  end

  def update_discovered_user_status
    discovered_user = DiscoveredUser.find_by(
      email: self.email,
      company_id: self.company_id,
      status: 'imported'
    )
    discovered_user.update_columns(status: 'ready_for_import') if discovered_user.present?
  end

  def avatar
    self.user_avatar ? self.user_avatar.as_json[:attachment] : nil
  end

  def avatar_thumb
    self.user_avatar&.as_json&.dig(:attachment, :url)
  end

  def update_help_ticket_creator
    return unless self.user && self.company 
    value_ids = company.custom_form_values.includes(:custom_form_field)
      .where(custom_form_fields: { name: "created_by" })
      .where(value_str: user.email)
      .where(value_int: nil)
      .pluck(:id)
    return unless value_ids.present?
    CustomFormValue.where(id: value_ids).
      update_all(value_int: contributor_id, value_str: nil, value_str_tokens: nil)
  end

  def experience_points_summary
    self.experience_point.summary if self.experience_point.present?
  end

  def create_remote_user
    Events::CreateRemoteUserWorker.perform_async({ id: self.id }.as_json)
  end

  def create_intercom_user
    if (Rails.env.production? || Rails.env.test?) && !company.is_sample_company && !user.super_admin?
      Events::CreateIntercomUserWorker.perform_async({ id: self.id }.as_json)
    end
  end

  def convert_ticket_emails(preloaded_ticket_emails = nil)
    if self.user && self.company
      ticket_emails = preloaded_ticket_emails
      ticket_emails ||= self.company
                            .ticket_emails.tickets_not_created
                            .where('ticket_emails.from like ? or ticket_emails.from = ?', "%<#{self.email}>%", self.email)
      HelpDesk::InboundEmail::TicketEmailService.new(self.company, ticket_emails).create_tickets_from_emails("company_user") if ticket_emails.present?
    end
  end

  def remove_associations
    if self.company
      self.company_user_mailers.destroy_all
      remove_associated_assets
    end
  end

  def remove_associated_assets
    AssignmentInformation.joins(managed_asset: :company)
      .where(managed_assets: { company_id: company.id }, assignment_informations: { used_by_contributor_id: self.contributor_id })
      .update_all(used_by_contributor_id: nil)
    AssignmentInformation.joins(managed_asset: :company)
      .where(managed_assets: { company_id: company.id }, assignment_informations: { managed_by_contributor_id: self.contributor_id })
      .update_all(managed_by_contributor_id: nil)
  end

  def remove_company_user_linkables
    self.destroy_linkables
  end

  def remove_company_user_vendor_contacts
    Vendor.where("primary_internal_contact_id = :id OR secondary_internal_contact_id = :id", id: self.id)
          .find_each do |vendor|
            if vendor.primary_internal_contact_id == self.id
              new_primary_id = vendor.secondary_internal_contact_id
              vendor.update(
                primary_internal_contact_id: new_primary_id,
                secondary_internal_contact_id: nil
              )
            elsif vendor.secondary_internal_contact_id == self.id
              vendor.update(secondary_internal_contact_id: nil)
            end
          end
  end

  def format_numbers
    self.work_phone = self.work_phone.delete('(').delete(')').delete('.').delete(' ').delete('-') if self.work_phone.present?
    self.mobile_phone = self.mobile_phone.delete('(').delete(')').delete('.').delete(' ').delete('-') if self.mobile_phone.present?
  end

  def assign_default_location
    if self.company.locations&.count == 1 && self.location.blank?
      self.location = self.company.locations.first
    end
  end

  def mfa_verification_required?
    self&.mfa_enabled && !self.mfa_verified
  end

  def sync_monitoring_remote_user
    Monitoring::SyncRemoteUserWorker.perform_in(5.seconds, self.company_id, self.guid, self.user_id) if self.type == 'CompanyMember'
  rescue => e
    Rails.logger.warn e.message
  end

  def create_user_activity
    changes = saved_changes.except("id", "created_at", "updated_at")
    if changes["archived_at"].present?
      activity_params = {
        activity_type: CompanyUserActivity.activity_types["archived"],
        owner_id: self.activity_owner_id,
        data: {
          current_value: changes["archived_at"][0].nil? ? "archived" : "unarchived"
        }
      }
    elsif changes["invite_token"].present? && !changes["granted_access_at"].present?
      activity_params = {
        activity_type: CompanyUserActivity.activity_types["invited"],
        owner_id: self.activity_owner_id,
        data: {
          current_value: changes["invite_token"][0].nil? ? "invited" : "re-invited"
        }
      }
    elsif changes["granted_access_at"].present?
      activity_params = {
        activity_type: CompanyUserActivity.activity_types["access"],
        owner_id: self.activity_owner_id,
        data: {
          current_value: "revoked"
        }
      }
    elsif changes["out_of_office"].present?
      activity_params = {
        activity_type: CompanyUserActivity.activity_types["updated"],
        owner_id: self.activity_owner_id,
        data: {
          activity_label: "out_of_office",
          current_value: changes["out_of_office"][1]
        }
      }
    elsif activity_type == "imported"
      activity_params = {
        activity_type: CompanyUserActivity.activity_types["imported"],
        owner_id: self.activity_owner_id,
      }
    elsif changes["custom_form_id"].present?
      activity_params = {
        activity_type: CompanyUserActivity.activity_types["updated"],
        owner_id: self.activity_owner_id,
        data: {
          current_value: self.custom_form.form_name,
          activity_label: "form",
        }
      }
    end
    self.activities.create(activity_params) if activity_params.present?
  end

  private

  def destroy_monitoring_remote_company_user
    Monitoring::DestroyRemoteCompanyUserWorker.perform_async(self.guid)
  rescue => e
    Rails.logger.warn e.message
  end

  def check_guests
    downcase_email = self.email.downcase
    guest = begin
      if company_guests.present?
        company_guests[downcase_email]
      else
        self.company&.guests.find_by(email: downcase_email)
      end
    end

    if guest
      self.contributor = guest.contributor
      guest.update_columns(contributor_id: nil)
      guest.destroy
    end
  end

  def create_contributor
    # Guests are scoped by workspace, so we need to make sure we merge in all of them
    custom_form_values = CustomFormValue.
                            joins(:custom_form_field).
                            joins("INNER JOIN guests ON guests.contributor_id = custom_form_values.value_int").
                            where(custom_form_fields: { field_attribute_type: :people_list}).
                            where(guests: { email: email.downcase }).
                            where(company_id: self.company_id)
    saved_ids = { }
    if custom_form_values.present?
      custom_form_values.find_each do |value|
        self.contributor_id ||= value.value_int
        saved_ids[value.value_int] = true
        value.value_int = self.contributor_id
        value.save!
      end
      Guest.where(contributor_id: saved_ids.keys).destroy_all if saved_ids.present?
      self.save!
    elsif !self.contributor && !destroyed? && !self.user.super_admin
      self.contributor = self.company.contributors.create
      update_column(:contributor_id, self.contributor.id)
    end
  end

  def update_subscription
    if self.subscriptions.present?
      last_admin = self.company.admin_company_users.where.not(id: self).last
      self.subscriptions.update_all(email: last_admin.email , subscriber_id: last_admin.id)
    end

    subscriptions = self.company.subscriptions

    subscriptions&.each do |subscription|
      subscription.secondary_emails&.each do |secondary_email|
        if self.email.include?(secondary_email['email'])
          subscription.update(
            secondary_emails: subscription.secondary_emails.reject! {
            |sec_email| sec_email['email'] == self.email
            }
          )
        end
      end
    end
  end

  def is_user_or_company_id_changed?
    errors = nil
    errors = "User id is changing from #{self.user_id_was} to #{self.user_id} for the company user #{self.id}" if self.user_id_changed?
    if self.company_id_changed?
      if errors.present?
        errors += " and company id is changing from #{self.company_id_was} to #{self.company_id} for the company user #{self.id}"
      else
        errors = "Company id is changing from #{self.company_id_was} to #{self.company_id} for the company user #{self.id}"
      end
    end
    if errors.present?
      raise errors
    end
    return true
  end

  def set_form_field_permissions
    return if self.company.company_users.count == 1
    form_ids = helpdesk_cf_ids || self.company.custom_forms.where(company_module: 'helpdesk').pluck(:id)
    SetNewUserExpandedFormFieldsPermission.new(form_ids).call
  end

  def link_with_integration_user
    intg_user = begin
      if integration_users.present?
        integration_users[self.email]
      else
        Integrations::User.find_by(email: self.email, company_id: self.company_id)
      end
    end
    intg_user.update_columns(company_user_id: self.id) if intg_user.present?
  end

  def create_company_ids_cache
    key = [name: 'companies_id', user_id: self.user_id]
    Rails.cache.delete(key) if Rails.cache.exist?(key)
    cache_company_ids(key)
  end

  def cache_company_ids(key)
    Rails.cache.fetch(key, :expires_in => 16.hours) do
      self.user.companies.pluck(:id)
    end
  end

  def delete_company_ids_cache
    if self&.user_id
      key = [name: 'companies_id', user_id: self.user_id]
      Rails.cache.delete(key) if Rails.cache.exist?(key)
    end
  end

  def create_company_domain
    return if self.super_admin?

    domain_name = extract_domain(self.email)
    company_domains = self.company.company_domains

    unless company_domains.exists?(domain_name: domain_name)
      company_domains.create(domain_name: domain_name, is_registered: domain_name == self.company.subdomain)
    end
  end

  def destroy_company_domain
    extracted_domain = extract_domain(self.email)

    other_users_with_same_domain = self.company.users.exists?(["email LIKE ?", "%@#{extracted_domain}.%"])

    unless other_users_with_same_domain
      self.company.company_domains.where(domain_name: extracted_domain).destroy_all
    end
  end

  def extract_domain(email)
    email.split('@').last.split('.').first if email
  end

  def archived_or_status_changed?
    self.saved_change_to_attribute?(:archived_at) || self.saved_change_to_attribute?(:out_of_office)
  end

  def store_user_name_in_asset_connector_log
    self.asset_connector_logs.update_all(owner_name: self.name)
  end

  def use_preloaded_validation?
    preloaded_existing_users.present?
  end

  def validate_uniqueness_with_preloaded_data
    if preloaded_existing_users[user_id] && preloaded_existing_users[user_id].id != id
      errors.add(:user_id, "has already been taken")
    end
  end
end
