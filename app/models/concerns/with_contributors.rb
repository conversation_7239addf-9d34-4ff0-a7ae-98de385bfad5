module WithContributors
  extend ActiveSupport::Concern

  included do
    after_save :create_contributor

    def create_contributor
      unless self.contributor || destroyed? || (self.class.name == "CompanyUser" && self.user.super_admin)
        # Check if this is part of a bulk operation to avoid N+1 queries
        if respond_to?(:is_bulk_action) && is_bulk_action
          # For bulk operations, we'll handle contributor creation separately
          return
        end

        self.contributor = self.company.contributors.create
        update_column(:contributor_id, self.contributor.id)
      end
    end
  end
end
