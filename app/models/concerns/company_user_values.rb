module CompanyUserValues
  extend ActiveSupport::Concern

  included do
    def user_avatar
      custom_form_values.joins(:custom_form_field).where(custom_form_fields: { field_attribute_type: "avatar" }).order('order_position').first
    end

    def mobile_phone
      if @mobile_phone.blank?
        @mobile_phone = begin
          cfv = get_cached_form_value("mobile_phone")&.value_str
          if cfv.present? && cfv.split(',')[1].present?
            cfv
          else
            ''
          end
        end
      end
      @mobile_phone
    end

    def location_ids
      custom_form_values.joins(:custom_form_field).where(custom_form_fields: { field_attribute_type: "location_list" }).pluck(:value_int).uniq
    end

    def title
      get_cached_form_value("title")&.value_str
    end

    def department
      get_cached_form_value("department")&.value_str
    end

    def supervisor_id
      get_cached_form_value("supervisor")&.value_int
    end

    def creator_contributor
      nil
    end

    private

    def get_cached_form_value(field_name)
      # Cache custom form values to avoid N+1 queries during bulk operations
      @cached_form_values ||= custom_form_values.includes(:custom_form_field).to_a
      @cached_form_values.find { |value| value.custom_form_field.name == field_name }
    end
  end
end
