module Linkables
  extend ActiveSupport::Concern

  included do
    has_one :linkable, as: 'linkable', dependent: :destroy
    after_commit :create_linkable, on: :create, unless: :system_or_sample_company_user?
    after_commit :update_linkable, on: :update, unless: :system_or_sample_company_user?
    before_destroy :destroy_linkables
    delegate :source_linkable_links, to: :linkable
    delegate :target_linkable_links, to: :linkable

    def create_linkable
      # Check if this is part of a bulk operation to avoid N+1 queries
      if respond_to?(:is_bulk_action) && is_bulk_action
        # For bulk operations, we'll handle linkable creation separately
        return
      end

      Linkable.create_or_find_by(company: self.company, linkable: self) do |linkable|
        linkable.name = get_self_name
      end
    end

    def update_linkable
      if get_self_name && self.linkable && self.linkable.name != get_self_name
        self.linkable.name = get_self_name
        self.linkable.save!
      end
    end

    def destroy_linkables
      self.linkable&.source_linkable_links&.destroy_all
      self.linkable&.target_linkable_links&.destroy_all
    end

    def class_list
      [
        "Location",
        "CompanyUser",
        "Group",
        "ManagedAsset",
        "Vendor",
        "Contract",
        "TelecomService",
        "HelpTicket",
        "GeneralTransaction"
      ]
    end

    def system_or_sample_company_user?
      self.class == SystemUser || (['CompanyUser', 'CompanyMember'].include?(self.class.name) && self.is_sample_company_user)
    end

    def get_self_name
      case self.class.name
      when 'HelpTicket'
        self.subject
      when 'GeneralTransaction'
        self.product_name
      else
        self.name
      end
    end
  end
end
